from sqlmodel import Session, SQLModel, create_engine
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
import databases
from typing import AsyncGenerator

from app.config import settings

# Create the synchronous database engine (for compatibility)
connect_args = {}
if "mysql" in settings.DATABASE_URL:
    connect_args = {
        "charset": "utf8mb4",
        "use_unicode": True,
        "autocommit": False
    }

engine = create_engine(
    settings.DATABASE_URL,
    echo=True,
    pool_size=10 if "mysql" in settings.DATABASE_URL else 5,
    max_overflow=20 if "mysql" in settings.DATABASE_URL else 10,
    pool_timeout=30,
    pool_recycle=3600,
    pool_pre_ping=True,
    connect_args=connect_args
)

# Create async database engine
async_database_url = settings.DATABASE_URL
if "mysql" in settings.DATABASE_URL:
    # Convert mysql:// to mysql+aiomysql://
    async_database_url = settings.DATABASE_URL.replace("mysql://", "mysql+aiomysql://")

async_engine = create_async_engine(
    async_database_url,
    echo=True,
    pool_size=10 if "mysql" in settings.DATABASE_URL else 5,
    max_overflow=20 if "mysql" in settings.DATABASE_URL else 10,
    pool_timeout=30,
    pool_recycle=3600,
    pool_pre_ping=True,
)

# Create async session factory
AsyncSessionLocal = sessionmaker(
    bind=async_engine,
    class_=AsyncSession,
    expire_on_commit=False
)

# Create databases instance for raw async queries
database = databases.Database(async_database_url)


async def init_db():
    """Initialize database tables"""
    async with async_engine.begin() as conn:
        await conn.run_sync(SQLModel.metadata.create_all)


def init_db_sync():
    """Synchronous database initialization for compatibility"""
    SQLModel.metadata.create_all(engine)


async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """Get async database session"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()


def get_session():
    """Synchronous session for compatibility"""
    with Session(engine) as session:
        yield session

# Alias for get_session to maintain compatibility with different naming conventions
get_db = get_session
