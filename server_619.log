nohup: ignoring input
2025-07-29 19:51:13,737 - app.main - INFO - Request logging middleware initialized and ready to capture requests
INFO:     Started server process [37082]
INFO:     Waiting for application startup.
2025-07-29 19:51:13,741 - app.main - INFO - Initializing application
2025-07-29 19:51:13,742 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 19:51:13,742 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 19:51:13,742 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("mcc_analysis_gemini")
2025-07-29 19:51:13,742 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("mcc_analysis_gemini")
2025-07-29 19:51:13,742 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 19:51:13,742 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 19:51:13,743 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("website_urls_gemini")
2025-07-29 19:51:13,743 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("website_urls_gemini")
2025-07-29 19:51:13,743 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 19:51:13,743 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 19:51:13,743 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("general_logs_gemini")
2025-07-29 19:51:13,743 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("general_logs_gemini")
2025-07-29 19:51:13,743 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 19:51:13,743 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 19:51:13,743 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("gemini_api_log_gemini")
2025-07-29 19:51:13,743 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("gemini_api_log_gemini")
2025-07-29 19:51:13,743 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 19:51:13,743 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 19:51:13,744 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("scrape_request_tracker_gemini")
2025-07-29 19:51:13,744 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("scrape_request_tracker_gemini")
2025-07-29 19:51:13,744 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 19:51:13,744 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 19:51:13,744 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("scraped_urls_gemini")
2025-07-29 19:51:13,744 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("scraped_urls_gemini")
2025-07-29 19:51:13,744 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 19:51:13,744 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 19:51:13,744 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("websites_gemini")
2025-07-29 19:51:13,744 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("websites_gemini")
2025-07-29 19:51:13,744 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 19:51:13,744 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 19:51:13,744 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("mcc_url_classification_gemini")
2025-07-29 19:51:13,744 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("mcc_url_classification_gemini")
2025-07-29 19:51:13,744 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 19:51:13,744 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 19:51:13,744 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 19:51:13,744 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-29 19:51:13,745 - app.main - INFO - Database initialized successfully
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
2025-07-29 19:51:23,341 - app.routers.mcc_analysis - INFO - Processing MCC analysis request for https://drynotch.com with ref_id 208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd
[2025-07-29 19:51:23][request_middleware][NO_REF] INFO: Request started: POST /mcc-analysis/
{
  "request_id": "4279e1e8-90c9-49d2-a0bf-3198ca46e488",
  "client_ip": "127.0.0.1",
  "user_agent": "Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0",
  "query_params": null,
  "headers": {
    "host": "127.0.0.1:8000",
    "user-agent": "Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0",
    "accept": "application/json",
    "accept-language": "en-US,en;q=0.5",
    "accept-encoding": "gzip, deflate, br, zstd",
    "referer": "http://127.0.0.1:8000/docs",
    "content-type": "application/json",
    "content-length": "3199",
    "origin": "http://127.0.0.1:8000",
    "connection": "keep-alive",
    "cookie": "username-127-0-0-1-8888=\"2|1:0|10:1753687981|23:username-127-0-0-1-8888|44:OTI1NWYwNDA2MmYzNGM4ODhmZWEyMzU0NDNhZWYzZGU=|af37f3c5748e269f0b3a691141a8d9d34952092469c0df3eaa2fabbf0681f48d\"; _xsrf=2|a122885f|aa0d74d2af0b3aa32b5dde71a04b1553|1753687981",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "priority": "u=0"
  },
  "url_full": "http://127.0.0.1:8000/mcc-analysis/"
}
2025-07-29 19:51:23,342 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 19:51:23,342 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 19:51:23,350 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = ?
2025-07-29 19:51:23,350 - sqlalchemy.engine.Engine - INFO - SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = ?
2025-07-29 19:51:23,350 INFO sqlalchemy.engine.Engine [generated in 0.00031s] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd',)
2025-07-29 19:51:23,350 - sqlalchemy.engine.Engine - INFO - [generated in 0.00031s] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd',)
2025-07-29 19:51:23,353 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 19:51:23,353 - sqlalchemy.engine.Engine - INFO - SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 19:51:23,353 INFO sqlalchemy.engine.Engine [generated in 0.00020s] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd',)
2025-07-29 19:51:23,353 - sqlalchemy.engine.Engine - INFO - [generated in 0.00020s] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd',)
2025-07-29 19:51:23,359 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,359 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,359 INFO sqlalchemy.engine.Engine [generated in 0.00049s (insertmanyvalues) 1/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,359 - sqlalchemy.engine.Engine - INFO - [generated in 0.00049s (insertmanyvalues) 1/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,360 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,360 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,360 INFO sqlalchemy.engine.Engine [insertmanyvalues 2/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'http://drynotch.com/cdn/shop/files/Untitled-2_dbc3a50a-6538-408a-b16d-aec289edf49d.png?v=1714240398', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,360 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 2/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'http://drynotch.com/cdn/shop/files/Untitled-2_dbc3a50a-6538-408a-b16d-aec289edf49d.png?v=1714240398', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,360 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,360 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,360 INFO sqlalchemy.engine.Engine [insertmanyvalues 3/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-high-waist-leggings-blue-crush', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,360 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 3/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-high-waist-leggings-blue-crush', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,360 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,360 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,360 INFO sqlalchemy.engine.Engine [insertmanyvalues 4/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/pages/about-us', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,360 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 4/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/pages/about-us', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,360 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,360 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,360 INFO sqlalchemy.engine.Engine [insertmanyvalues 5/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/collections/top-wear', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,360 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 5/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/collections/top-wear', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,360 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,360 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,360 INFO sqlalchemy.engine.Engine [insertmanyvalues 6/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/cart', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,360 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 6/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/cart', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,360 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,360 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,360 INFO sqlalchemy.engine.Engine [insertmanyvalues 7/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/pages/size-chart', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,360 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 7/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/pages/size-chart', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,360 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,360 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,360 INFO sqlalchemy.engine.Engine [insertmanyvalues 8/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/policies/privacy-policy', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,360 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 8/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/policies/privacy-policy', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,360 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,360 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,360 INFO sqlalchemy.engine.Engine [insertmanyvalues 9/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/collections', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,360 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 9/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/collections', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,360 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,360 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,360 INFO sqlalchemy.engine.Engine [insertmanyvalues 10/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/collections/co-ord-sets', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,360 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 10/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/collections/co-ord-sets', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,360 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,360 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,360 INFO sqlalchemy.engine.Engine [insertmanyvalues 11/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/pages/contact', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,360 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 11/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/pages/contact', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,360 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,360 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,361 INFO sqlalchemy.engine.Engine [insertmanyvalues 12/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/blue-crush-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,361 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 12/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/blue-crush-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,361 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,361 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,361 INFO sqlalchemy.engine.Engine [insertmanyvalues 13/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/work-it-out-straight-fit-pants', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,361 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 13/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/work-it-out-straight-fit-pants', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,361 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,361 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,361 INFO sqlalchemy.engine.Engine [insertmanyvalues 14/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/yoga-lite-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,361 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 14/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/yoga-lite-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,361 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,361 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,361 INFO sqlalchemy.engine.Engine [insertmanyvalues 15/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/collections/best-sellers', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,361 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 15/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/collections/best-sellers', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,361 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,361 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,361 INFO sqlalchemy.engine.Engine [insertmanyvalues 16/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/account/login', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,361 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 16/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/account/login', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,361 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,361 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,361 INFO sqlalchemy.engine.Engine [insertmanyvalues 17/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/fiery-trio-co-ord-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,361 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 17/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/fiery-trio-co-ord-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,361 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,361 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,361 INFO sqlalchemy.engine.Engine [insertmanyvalues 18/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/mauve-mist-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,361 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 18/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/mauve-mist-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,361 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,361 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,361 INFO sqlalchemy.engine.Engine [insertmanyvalues 19/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/search?type=product&options%5Bunavailable_products%5D=last&options%5Bprefix%5D=last&&q=Co-ords', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,361 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 19/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/search?type=product&options%5Bunavailable_products%5D=last&options%5Bprefix%5D=last&&q=Co-ords', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,361 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,361 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,361 INFO sqlalchemy.engine.Engine [insertmanyvalues 20/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/collections/frontpage', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,361 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 20/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/collections/frontpage', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,372 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,372 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,372 INFO sqlalchemy.engine.Engine [insertmanyvalues 21/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/neon-attack-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,372 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 21/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/neon-attack-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,372 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,372 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,372 INFO sqlalchemy.engine.Engine [insertmanyvalues 22/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/navy-nirvana-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,372 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 22/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/navy-nirvana-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,372 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,372 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,372 INFO sqlalchemy.engine.Engine [insertmanyvalues 23/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/neon-attack-co-ord-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,372 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 23/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/neon-attack-co-ord-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,372 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,372 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,372 INFO sqlalchemy.engine.Engine [insertmanyvalues 24/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/citrus-blaze-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,372 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 24/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/citrus-blaze-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,372 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,372 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,372 INFO sqlalchemy.engine.Engine [insertmanyvalues 25/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/cdn/shop/files/Untitled-2_dbc3a50a-6538-408a-b16d-aec289edf49d.png?v=1714240398', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,372 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 25/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/cdn/shop/files/Untitled-2_dbc3a50a-6538-408a-b16d-aec289edf49d.png?v=1714240398', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,372 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,372 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,372 INFO sqlalchemy.engine.Engine [insertmanyvalues 26/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/policies/terms-of-service', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,372 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 26/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/policies/terms-of-service', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,372 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,372 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,372 INFO sqlalchemy.engine.Engine [insertmanyvalues 27/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/account/register', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,372 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 27/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/account/register', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,372 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,372 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,373 INFO sqlalchemy.engine.Engine [insertmanyvalues 28/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/sleek-monochrome-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,373 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 28/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/sleek-monochrome-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,373 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,373 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,373 INFO sqlalchemy.engine.Engine [insertmanyvalues 29/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/search?type=product&options%5Bunavailable_products%5D=last&options%5Bprefix%5D=last&&q=Leggings', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,373 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 29/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/search?type=product&options%5Bunavailable_products%5D=last&options%5Bprefix%5D=last&&q=Leggings', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,373 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,373 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,373 INFO sqlalchemy.engine.Engine [insertmanyvalues 30/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/pages/faqs', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,373 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 30/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/pages/faqs', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,373 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,373 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,373 INFO sqlalchemy.engine.Engine [insertmanyvalues 31/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-dare-to-flare-leggings', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,373 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 31/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-dare-to-flare-leggings', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,373 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,373 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,373 INFO sqlalchemy.engine.Engine [insertmanyvalues 32/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-neon-attack-leggings', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,373 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 32/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-neon-attack-leggings', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,373 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,373 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,373 INFO sqlalchemy.engine.Engine [insertmanyvalues 33/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-yoga-lite-leggings-black', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,373 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 33/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-yoga-lite-leggings-black', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,373 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,373 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,373 INFO sqlalchemy.engine.Engine [insertmanyvalues 34/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/flex-on-quarter-zip-co-ord-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,373 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 34/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/flex-on-quarter-zip-co-ord-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,373 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,373 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,373 INFO sqlalchemy.engine.Engine [insertmanyvalues 35/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/cdn', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,373 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 35/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/cdn', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,373 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,373 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,373 INFO sqlalchemy.engine.Engine [insertmanyvalues 36/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/search?type=product&options%5Bunavailable_products%5D=last&options%5Bprefix%5D=last&&q=Sports+Bra', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,373 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 36/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/search?type=product&options%5Bunavailable_products%5D=last&options%5Bprefix%5D=last&&q=Sports+Bra', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,373 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,373 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,373 INFO sqlalchemy.engine.Engine [insertmanyvalues 37/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/collections/crop-tops', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,373 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 37/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/collections/crop-tops', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,374 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,374 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,374 INFO sqlalchemy.engine.Engine [insertmanyvalues 38/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-dare-to-flare-leggings#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,374 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 38/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-dare-to-flare-leggings#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,374 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,374 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,374 INFO sqlalchemy.engine.Engine [insertmanyvalues 39/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/fiery-trio-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,374 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 39/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/fiery-trio-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,374 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,374 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,374 INFO sqlalchemy.engine.Engine [insertmanyvalues 40/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/citrus-blaze-co-ord-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,374 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 40/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/citrus-blaze-co-ord-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,374 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,374 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,374 INFO sqlalchemy.engine.Engine [insertmanyvalues 41/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-high-waist-ribbed-tights', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,374 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 41/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-high-waist-ribbed-tights', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,374 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,374 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,374 INFO sqlalchemy.engine.Engine [insertmanyvalues 42/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/collections/bottom-wear', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,374 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 42/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/collections/bottom-wear', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,374 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,374 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,374 INFO sqlalchemy.engine.Engine [insertmanyvalues 43/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/blue-crush-co-ord-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,374 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 43/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/blue-crush-co-ord-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,374 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,374 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,374 INFO sqlalchemy.engine.Engine [insertmanyvalues 44/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/pages/wishlist', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,374 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 44/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/pages/wishlist', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,374 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,374 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,374 INFO sqlalchemy.engine.Engine [insertmanyvalues 45/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/collections/all', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,374 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 45/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/collections/all', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,374 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,374 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,374 INFO sqlalchemy.engine.Engine [insertmanyvalues 46/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/policies/refund-policy', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,374 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 46/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/policies/refund-policy', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,374 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,374 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,375 INFO sqlalchemy.engine.Engine [insertmanyvalues 47/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/wpm@a7f653a7w82d20f99pe720974fm204fcef1/custom/web-pixel-shopify-custom-pixel@0420/sandbox/modern', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,375 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 47/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/wpm@a7f653a7w82d20f99pe720974fm204fcef1/custom/web-pixel-shopify-custom-pixel@0420/sandbox/modern', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,375 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,375 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:51:23,375 INFO sqlalchemy.engine.Engine [insertmanyvalues 48/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/pages/shipping', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,375 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 48/48 (ordered; batch not supported)] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 'https://drynotch.com/pages/shipping', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 19:51:23,375 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 19:51:23,375 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-29 19:51:23,378 - app.utils.website_url_processor - INFO - Successfully stored 48 URLs for scrape_request_ref_id: 208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd
2025-07-29 19:51:23,378 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 19:51:23,378 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 19:51:23,379 INFO sqlalchemy.engine.Engine INSERT INTO mcc_analysis_gemini (website, scrape_request_ref_id, result_status, mcc_code, business_category, business_description, reasoning, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-29 19:51:23,379 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_analysis_gemini (website, scrape_request_ref_id, result_status, mcc_code, business_category, business_description, reasoning, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-29 19:51:23,379 INFO sqlalchemy.engine.Engine [generated in 0.00021s] ('https://drynotch.com', '208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', None, None, None, None, None, '2025-07-29T19:51:23.378335Z', None, None, None, None, None, None, 'default', 'PENDING')
2025-07-29 19:51:23,379 - sqlalchemy.engine.Engine - INFO - [generated in 0.00021s] ('https://drynotch.com', '208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', None, None, None, None, None, '2025-07-29T19:51:23.378335Z', None, None, None, None, None, None, 'default', 'PENDING')
2025-07-29 19:51:23,380 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 19:51:23,380 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-29 19:51:23,382 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 19:51:23,382 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 19:51:23,383 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = ?
2025-07-29 19:51:23,383 - sqlalchemy.engine.Engine - INFO - SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = ?
2025-07-29 19:51:23,383 INFO sqlalchemy.engine.Engine [generated in 0.00012s] (29,)
2025-07-29 19:51:23,383 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] (29,)
2025-07-29 19:51:23,384 - app.routers.mcc_analysis - INFO - Created new MCC analysis with ID 29
2025-07-29 19:51:23,384 - app.routers.mcc_analysis - INFO - === QUEUEING BACKGROUND TASK for analysis 29 ===
2025-07-29 19:51:23,384 - app.routers.mcc_analysis - INFO - Background task successfully queued for analysis 29
2025-07-29 19:51:23,384 - app.routers.mcc_analysis - INFO - Task will process: website=https://drynotch.com, scrape_ref=208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd
2025-07-29 19:51:23,384 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-29 19:51:23,384 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-07-29 19:51:23][request_middleware][NO_REF] INFO: Request completed: POST /mcc-analysis/ - 200
{
  "request_id": "4279e1e8-90c9-49d2-a0bf-3198ca46e488",
  "status_code": 200,
  "response_time_ms": 45.45
}
INFO:     127.0.0.1:57484 - "POST /mcc-analysis/ HTTP/1.1" 200 OK
2025-07-29 19:51:24,962 - app.routers.mcc_analysis - INFO - === BACKGROUND TASK STARTED for MCC analysis ID: 29 ===
2025-07-29 19:51:24,963 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 19:51:24,963 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 19:51:24,964 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id AS mcc_analysis_gemini_id, mcc_analysis_gemini.website AS mcc_analysis_gemini_website, mcc_analysis_gemini.scrape_request_ref_id AS mcc_analysis_gemini_scrape_request_ref_id, mcc_analysis_gemini.result_status AS mcc_analysis_gemini_result_status, mcc_analysis_gemini.mcc_code AS mcc_analysis_gemini_mcc_code, mcc_analysis_gemini.business_category AS mcc_analysis_gemini_business_category, mcc_analysis_gemini.business_description AS mcc_analysis_gemini_business_description, mcc_analysis_gemini.reasoning AS mcc_analysis_gemini_reasoning, mcc_analysis_gemini.created_at AS mcc_analysis_gemini_created_at, mcc_analysis_gemini.started_at AS mcc_analysis_gemini_started_at, mcc_analysis_gemini.completed_at AS mcc_analysis_gemini_completed_at, mcc_analysis_gemini.failed_at AS mcc_analysis_gemini_failed_at, mcc_analysis_gemini.last_updated AS mcc_analysis_gemini_last_updated, mcc_analysis_gemini.error_message AS mcc_analysis_gemini_error_message, mcc_analysis_gemini.details AS mcc_analysis_gemini_details, mcc_analysis_gemini.org_id AS mcc_analysis_gemini_org_id, mcc_analysis_gemini.processing_status AS mcc_analysis_gemini_processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = ?
2025-07-29 19:51:24,964 - sqlalchemy.engine.Engine - INFO - SELECT mcc_analysis_gemini.id AS mcc_analysis_gemini_id, mcc_analysis_gemini.website AS mcc_analysis_gemini_website, mcc_analysis_gemini.scrape_request_ref_id AS mcc_analysis_gemini_scrape_request_ref_id, mcc_analysis_gemini.result_status AS mcc_analysis_gemini_result_status, mcc_analysis_gemini.mcc_code AS mcc_analysis_gemini_mcc_code, mcc_analysis_gemini.business_category AS mcc_analysis_gemini_business_category, mcc_analysis_gemini.business_description AS mcc_analysis_gemini_business_description, mcc_analysis_gemini.reasoning AS mcc_analysis_gemini_reasoning, mcc_analysis_gemini.created_at AS mcc_analysis_gemini_created_at, mcc_analysis_gemini.started_at AS mcc_analysis_gemini_started_at, mcc_analysis_gemini.completed_at AS mcc_analysis_gemini_completed_at, mcc_analysis_gemini.failed_at AS mcc_analysis_gemini_failed_at, mcc_analysis_gemini.last_updated AS mcc_analysis_gemini_last_updated, mcc_analysis_gemini.error_message AS mcc_analysis_gemini_error_message, mcc_analysis_gemini.details AS mcc_analysis_gemini_details, mcc_analysis_gemini.org_id AS mcc_analysis_gemini_org_id, mcc_analysis_gemini.processing_status AS mcc_analysis_gemini_processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = ?
2025-07-29 19:51:24,964 INFO sqlalchemy.engine.Engine [generated in 0.00015s] (29,)
2025-07-29 19:51:24,964 - sqlalchemy.engine.Engine - INFO - [generated in 0.00015s] (29,)
2025-07-29 19:51:24,966 INFO sqlalchemy.engine.Engine UPDATE mcc_analysis_gemini SET started_at=?, processing_status=? WHERE mcc_analysis_gemini.id = ?
2025-07-29 19:51:24,966 - sqlalchemy.engine.Engine - INFO - UPDATE mcc_analysis_gemini SET started_at=?, processing_status=? WHERE mcc_analysis_gemini.id = ?
2025-07-29 19:51:24,966 INFO sqlalchemy.engine.Engine [generated in 0.00012s] ('2025-07-29T19:51:24.965112Z', 'PROCESSING', 29)
2025-07-29 19:51:24,966 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ('2025-07-29T19:51:24.965112Z', 'PROCESSING', 29)
2025-07-29 19:51:24,966 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 19:51:24,966 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-29 19:51:24,968 - app.routers.mcc_analysis - INFO - Updated MccAnalysis 29 status to PROCESSING
2025-07-29 19:51:24,968 - app.routers.mcc_analysis - INFO - Starting MCC classification service for analysis 29
[2025-07-29 19:51:24][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Starting MCC analysis process
{
  "scrape_request_ref_id": "208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd",
  "org_id": "default"
}
[2025-07-29 19:51:24][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Step 1: Getting URLs data
[2025-07-29 19:51:24][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Checking if URLs exist in database
{
  "scrape_request_ref_id": "208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd"
}
2025-07-29 19:51:24,968 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 19:51:24,968 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 19:51:24,968 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 19:51:24,968 - sqlalchemy.engine.Engine - INFO - SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 19:51:24,968 INFO sqlalchemy.engine.Engine [cached since 1.616s ago] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd',)
2025-07-29 19:51:24,968 - sqlalchemy.engine.Engine - INFO - [cached since 1.616s ago] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd',)
2025-07-29 19:51:24,970 - app.utils.website_url_processor - INFO - Retrieved 48 URLs for scrape_request_ref_id: 208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd
2025-07-29 19:51:24,970 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = ?
2025-07-29 19:51:24,970 - sqlalchemy.engine.Engine - INFO - SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = ?
2025-07-29 19:51:24,970 INFO sqlalchemy.engine.Engine [cached since 1.62s ago] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd',)
2025-07-29 19:51:24,970 - sqlalchemy.engine.Engine - INFO - [cached since 1.62s ago] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd',)
[2025-07-29 19:51:24][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: URLs found in database
{
  "website": "https://drynotch.com",
  "total_depths": 1,
  "total_urls": 48
}
[2025-07-29 19:51:24][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Successfully unpacked URL check result
{
  "urls_exist": true,
  "urls_data_type": "<class 'dict'>"
}
[2025-07-29 19:51:24][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Total parsed URLs found: 48
[2025-07-29 19:51:24][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Step 2: Preparing data for analysis
[2025-07-29 19:51:24][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Preparing data for MCC analysis
{
  "website": "https://drynotch.com"
}
[2025-07-29 19:51:24][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Got URLs by depth
{
  "depth_1_count": 48,
  "depth_2_count": 0,
  "scrape_request_ref_id": "208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd"
}
[2025-07-29 19:51:24][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Checking for existing classification results in database
2025-07-29 19:51:24,971 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 19:51:24,971 - sqlalchemy.engine.Engine - INFO - SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 19:51:24,971 INFO sqlalchemy.engine.Engine [cached since 1.618s ago] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd',)
2025-07-29 19:51:24,971 - sqlalchemy.engine.Engine - INFO - [cached since 1.618s ago] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd',)
2025-07-29 19:51:27,814 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[2025-07-29 19:51:24][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Found 48 stored URL records
[2025-07-29 19:51:24][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Stored classification analysis
{
  "total_stored_urls": 48,
  "unreachable_count": 48,
  "classified_count": 0,
  "stored_categories": {
    "urls_not_reachable": 48
  }
}
[2025-07-29 19:51:24][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] WARNING: [RACE CONDITION FIX] All stored URLs marked unreachable - forcing fresh classification
{
  "total_urls": 48,
  "unreachable_urls": 48,
  "classified_urls": 0,
  "action": "FORCING_FRESH_CLASSIFICATION",
  "scrape_ref": "208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd",
  "fix_type": "RACE_CONDITION_PROTECTION",
  "time": "2025-07-29T19:51:24.971851"
}
[2025-07-29 19:51:24][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: No usable stored classification results found, proceeding with fresh API calls
[2025-07-29 19:51:24][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Starting soft classification of URLs
[2025-07-29 19:51:24][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Starting soft classification
{
  "website": "https://drynotch.com",
  "urls_depth_1_count": 48,
  "urls_depth_2_count": 0
}
[2025-07-29 19:51:24][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Dictionary for soft classification prepared
{
  "url_count": 48,
  "sample_urls": [
    "https://drynotch.com",
    "http://drynotch.com/cdn/shop/files/Untitled-2_dbc3a50a-6538-408a-b16d-aec289edf49d.png?v=1714240398",
    "https://drynotch.com/products/notchflex-high-waist-leggings-blue-crush"
  ]
}
[2025-07-29 19:51:24][test-analysis][NO_REF] INFO: Starting URL processing for model policy result
{
  "website": "https://drynotch.com",
  "total_urls": 48
}
[2025-07-29 19:51:25][test-analysis][NO_REF] INFO: Token calculation: system=265, base_user=988, available_for_urls=87747
[2025-07-29 19:51:25][test-analysis][NO_REF] INFO: Total URL tokens: 817, Available: 87747
[2025-07-29 19:51:25][test-analysis][NO_REF] INFO: All URLs fit within token limit
[2025-07-29 19:51:25][test-analysis][NO_REF] INFO: Final URLs after token limiting
{
  "original_url_count": 48,
  "final_url_count": 48,
  "urls_trimmed": 0,
  "system_tokens": 265,
  "base_user_tokens": 988,
  "url_tokens": 817,
  "final_total_tokens": 2070,
  "token_limit": 90000,
  "remaining_tokens": 87930
}
[2025-07-29 19:51:25][test-analysis][NO_REF] INFO: Final prompt verification
{
  "actual_prompt_tokens": 2308,
  "token_limit": 90000,
  "within_limit": true,
  "processing_time": 0.1421520709991455
}
[2025-07-29 19:51:25][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Sending request to OpenAI API using model gpt-4o...
[2025-07-29 19:51:25][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Processing URLs for classification. This may take some time...
[2025-07-29 19:51:27][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Usage: CompletionUsage(completion_tokens=134, prompt_tokens=2318, total_tokens=2452, completion_tokens_details=CompletionTokensDetails(accepted_prediction_tokens=0, audio_tokens=0, reasoning_tokens=0, rejected_prediction_tokens=0), prompt_tokens_details=PromptTokensDetails(audio_tokens=0, cached_tokens=0))
[2025-07-29 19:51:28][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Soft classification response received
{
  "response_length": 444,
  "response_preview": "```json\n{\n    \"home_page\": [0],\n    \"about_us\": [3],\n    \"terms_and_condition\": [25],\n    \"returns_cancellation_exchange\": [45],\n    \"privacy_policy\": [7],\n    \"shipping_delivery\": [47],\n    \"contact_..."
}
[2025-07-29 19:51:28][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Preparing data for model policy result
{
  "website": "https://drynotch.com",
  "dictionary_1_count": 48,
  "total_chars": 2784,
  "urls_sample": [
    "https://drynotch.com",
    "http://drynotch.com/cdn/shop/files/Untitled-2_dbc3a50a-6538-408a-b16d-aec289edf49d.png?v=1714240398",
    "https://drynotch.com/products/notchflex-high-waist-leggings-blue-crush"
  ]
}
[2025-07-29 19:51:28][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Got policy URLs classification
{
  "policy_urls": {
    "home_page": [
      0
    ],
    "about_us": [
      3
    ],
    "terms_and_condition": [
      25
    ],
    "returns_cancellation_exchange": [
      45
    ],
    "privacy_policy": [
      7
    ],
    "shipping_delivery": [
      47
    ],
    "contact_us": [
      10
    ],
    "products": [
      2,
      11,
      12,
      16,
      22
    ],
    "services": [],
    "catalogue": [
      8
    ],
    "instagram_page": [],
    "facebook_page": [],
    "twitter_page": [],
    "linkedin_page": [],
    "youtube_page": [],
    "pinterest_page": []
  },
  "categories_found": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "products",
    "services",
    "catalogue",
    "instagram_page",
    "facebook_page",
    "twitter_page",
    "linkedin_page",
    "youtube_page",
    "pinterest_page"
  ]
}
[2025-07-29 19:51:28][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Completed soft classification
{
  "mcc_dict": {
    "home_page": [
      "https://drynotch.com"
    ],
    "about_us": [
      "https://drynotch.com/pages/about-us"
    ],
    "terms_and_condition": [
      "https://drynotch.com/policies/terms-of-service"
    ],
    "returns_cancellation_exchange": [
      "https://drynotch.com/policies/refund-policy"
    ],
    "privacy_policy": [
      "https://drynotch.com/policies/privacy-policy"
    ],
    "shipping_delivery": [
      "https://drynotch.com/pages/shipping"
    ],
    "contact_us": [
      "https://drynotch.com/pages/contact"
    ],
    "products": [
      "https://drynotch.com/products/notchflex-high-waist-leggings-blue-crush",
      "https://drynotch.com/products/blue-crush-co-ord-set#judgeme_product_reviews",
      "https://drynotch.com/products/work-it-out-straight-fit-pants",
      "https://drynotch.com/products/fiery-trio-co-ord-set",
      "https://drynotch.com/products/neon-attack-co-ord-set"
    ],
    "services": [],
    "catalogue": [
      "https://drynotch.com/collections"
    ],
    "instagram_page": [],
    "facebook_page": [],
    "twitter_page": [],
    "linkedin_page": [],
    "youtube_page": [],
    "pinterest_page": []
  },
  "total_classified_urls": 13,
  "priority_urls_count": 8
}
[2025-07-29 19:51:28][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Successfully unpacked soft classification result
{
  "output_df_type": "<class 'pandas.core.frame.DataFrame'>",
  "soft_classified_urls_type": "<class 'dict'>"
}
[2025-07-29 19:51:28][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Soft classification unreachable URL analysis
{
  "total_urls": 48,
  "unreachable_count": 0,
  "unreachable_ratio": "0/48",
  "unreachable_percentage": "0.0%",
  "urls_by_category": {
    "home_page": 1,
    "about_us": 1,
    "terms_and_condition": 1,
    "returns_cancellation_exchange": 1,
    "privacy_policy": 1,
    "shipping_delivery": 1,
    "contact_us": 1,
    "products": 5,
    "services": 0,
    "catalogue": 1,
    "instagram_page": 0,
    "facebook_page": 0,
    "twitter_page": 0,
    "linkedin_page": 0,
    "youtube_page": 0,
    "pinterest_page": 0
  }
}
[2025-07-29 19:51:28][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Soft classification completed
{
  "categories_found": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "products",
    "services",
    "catalogue",
    "instagram_page",
    "facebook_page",
    "twitter_page",
    "linkedin_page",
    "youtube_page",
    "pinterest_page"
  ]
}2025-07-29 19:51:32,859 - google_genai.models - INFO - AFC is enabled with max remote calls: 20000.
2025-07-29 19:51:50,665 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 200 OK"
2025-07-29 19:51:50,669 - google_genai.models - INFO - AFC remote call 1 is done.

[2025-07-29 19:51:28][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Starting hard classification of URLs
[2025-07-29 19:51:28][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Starting hard classification verification for POLICY URLs only
{
  "website": "https://drynotch.com"
}
[2025-07-29 19:51:28][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Including policy category 'home_page' in hard classification
{
  "url_count": 1
}
[2025-07-29 19:51:28][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Including policy category 'about_us' in hard classification
{
  "url_count": 1
}
[2025-07-29 19:51:28][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Including policy category 'terms_and_condition' in hard classification
{
  "url_count": 1
}
[2025-07-29 19:51:28][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Including policy category 'returns_cancellation_exchange' in hard classification
{
  "url_count": 1
}
[2025-07-29 19:51:28][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Including policy category 'privacy_policy' in hard classification
{
  "url_count": 1
}
[2025-07-29 19:51:28][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Including policy category 'shipping_delivery' in hard classification
{
  "url_count": 1
}
[2025-07-29 19:51:28][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Including policy category 'contact_us' in hard classification
{
  "url_count": 1
}
[2025-07-29 19:51:28][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Including other category 'products' in hard classification
{
  "url_count": 5
}
[2025-07-29 19:51:28][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Including other category 'services' in hard classification
{
  "url_count": 0
}
[2025-07-29 19:51:28][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Including policy category 'catalogue' in hard classification
{
  "url_count": 1
}
[2025-07-29 19:51:28][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Skipping social media category 'instagram_page' from hard classification
{
  "url_count": 0,
  "reason": "social_media_uses_soft_classification_only"
}
[2025-07-29 19:51:28][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Skipping social media category 'facebook_page' from hard classification
{
  "url_count": 0,
  "reason": "social_media_uses_soft_classification_only"
}
[2025-07-29 19:51:28][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Skipping social media category 'twitter_page' from hard classification
{
  "url_count": 0,
  "reason": "social_media_uses_soft_classification_only"
}
[2025-07-29 19:51:28][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Skipping social media category 'linkedin_page' from hard classification
{
  "url_count": 0,
  "reason": "social_media_uses_soft_classification_only"
}
[2025-07-29 19:51:28][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Skipping social media category 'youtube_page' from hard classification
{
  "url_count": 0,
  "reason": "social_media_uses_soft_classification_only"
}
[2025-07-29 19:51:28][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Skipping social media category 'pinterest_page' from hard classification
{
  "url_count": 0,
  "reason": "social_media_uses_soft_classification_only"
}
[2025-07-29 19:51:28][test-analysis][NO_REF] INFO: Hard classification input
{
  "total_urls": 13,
  "website": "https://drynotch.com",
  "url_limit": 20
}
[2025-07-29 19:51:28][test-analysis][NO_REF] INFO: All URLs fit within hard classification limit
{
  "total_urls": 13,
  "limit": 20
}
[2025-07-29 19:51:28][gemini_optimizer_post_soft_classification][NO_REF] INFO: Optimizing Gemini call for task_type: post_soft_classification
{
  "received_task_type": "post_soft_classification"
}
[2025-07-29 19:51:32][post_soft_classification_unknown][NO_REF] INFO: Starting Gemini API call
{
  "model": "gemini-2.5-flash",
  "timeout": 120,
  "max_retries": 3,
  "prompt_length": 5872,
  "context": {
    "task_type": "post_soft_classification"
  }
}
[2025-07-29 19:51:32][post_soft_classification_unknown][NO_REF] INFO: Gemini API attempt 1/3
[2025-07-29 19:51:50][post_soft_classification_unknown][NO_REF] INFO: Gemini API Usage: cache_tokens_details=None cached_content_token_count=None candidates_token_count=461 candidates_tokens_details=None prompt_token_count=1459 prompt_tokens_details=[ModalityTokenCount(
  modality=<MediaModality.TEXT: 'TEXT'>,
  token_count=1459
)] thoughts_token_count=1948 tool_use_prompt_token_count=None tool_use_prompt_tokens_details=None total_token_count=3868 traffic_type=None
[2025-07-29 19:51:50][post_soft_classification_unknown][NO_REF] INFO: Gemini API call successful
{
  "attempt": 1,
  "response_length": 345,
  "finish_reason": "STOP"
}
[2025-07-29 19:51:51][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Gemini response received for hard classification
{
  "response_length": 345,
  "response_preview": "```json\n{\n    \"home_page\": [3],\n    \"about_us\": [10],\n    \"terms_and_condition\": [],\n    \"returns_cancellation_exchange\": [0, 2, 4, 6, 7],\n    \"privacy_policy\": [],\n    \"shipping_delivery\": [0, 2, 4, ..."
}
[2025-07-29 19:51:51][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Parsing hard classification response
{
  "response": "```json\n{\n    \"home_page\": [3],\n    \"about_us\": [10],\n    \"terms_and_condition\": [],\n    \"returns_cancellation_exchange\": [0, 2, 4, 6, 7],\n    \"privacy_policy\": [],\n    \"shipping_delivery\": [0, 2, 4, 6, 7, 11],\n    \"contact_us\": [5],\n    \"catalogue\": [0, 1, 2, 4, 6, 7],\n    \"urls_not_reachable\": [],\n    \"Unreachable_via_tool\": [8, 9, 12]\n}\n```"
}
[2025-07-29 19:51:51][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Verified indices dictionary created
{
  "indices_categories": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "catalogue",
    "urls_not_reachable",
    "Unreachable_via_tool"
  ],
  "total_indices": 23
}
[2025-07-29 19:51:51][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Hard classification reachability results
{
  "unreachable_urls_count": 3,
  "reachable_urls_count": 20,
  "unreachable_urls": [
    8,
    9,
    12
  ],
  "website": "https://drynotch.com",
  "backup_flow_trigger": true
}
[2025-07-29 19:51:51][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Verified indices dictionary created
{
  "indices_categories": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "catalogue",
    "urls_not_reachable",
    "Unreachable_via_tool"
  ],
  "total_indices": 23
}
[2025-07-29 19:51:51][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Verified URLs dictionary created with social media merged
{
  "verified_categories": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "catalogue",
    "urls_not_reachable",
    "Unreachable_via_tool"
  ],
  "category_counts": {
    "home_page": 1,
    "about_us": 1,
    "terms_and_condition": 0,
    "returns_cancellation_exchange": 5,
    "privacy_policy": 0,
    "shipping_delivery": 6,
    "contact_us": 1,
    "catalogue": 6,
    "urls_not_reachable": 0,
    "Unreachable_via_tool": 3
  },
  "total_verified_urls": 23,
  "social_media_merged": true
}
[2025-07-29 19:51:51][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Completed hard classification verification with social media merge
{
  "verified_categories": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "catalogue",
    "urls_not_reachable",
    "Unreachable_via_tool"
  ],
  "total_verified_urls": 23,
  "policy_hard_classified": true,
  "social_media_soft_classified": true
}
[2025-07-29 19:51:51][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Saving hard classification results to database
2025-07-29 19:51:51,672 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 19:51:51,672 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 19:51:51,672 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 19:51:51,672 - sqlalchemy.engine.Engine - INFO - SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 19:51:51,672 INFO sqlalchemy.engine.Engine [cached since 28.32s ago] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd',)
2025-07-29 19:51:51,672 - sqlalchemy.engine.Engine - INFO - [cached since 28.32s ago] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd',)
[2025-07-29 19:51:51][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Retrieved 48 URL records for hard classification update
2025-07-29 19:51:51,675 INFO sqlalchemy.engine.Engine UPDATE website_urls_gemini SET hard_class=? WHERE website_urls_gemini.id = ?
2025-07-29 19:51:51,675 - sqlalchemy.engine.Engine - INFO - UPDATE website_urls_gemini SET hard_class=? WHERE website_urls_gemini.id = ?
2025-07-29 19:51:51,675 INFO sqlalchemy.engine.Engine [generated in 0.00018s] [('["home_page"]', 2306), ('["returns_cancellation_exchange", "shipping_delivery", "catalogue"]', 2308), ('["about_us"]', 2309), ('["Unreachable_via_tool"]', 2313), ('["catalogue"]', 2314), ('["contact_us"]', 2316), ('["returns_cancellation_exchange", "shipping_delivery", "catalogue"]', 2317), ('["returns_cancellation_exchange", "shipping_delivery", "catalogue"]', 2318)  ... displaying 10 of 13 total bound parameter sets ...  ('["Unreachable_via_tool"]', 2351), ('["shipping_delivery"]', 2353)]
2025-07-29 19:51:51,675 - sqlalchemy.engine.Engine - INFO - [generated in 0.00018s] [('["home_page"]', 2306), ('["returns_cancellation_exchange", "shipping_delivery", "catalogue"]', 2308), ('["about_us"]', 2309), ('["Unreachable_via_tool"]', 2313), ('["catalogue"]', 2314), ('["contact_us"]', 2316), ('["returns_cancellation_exchange", "shipping_delivery", "catalogue"]', 2317), ('["returns_cancellation_exchange", "shipping_delivery", "catalogue"]', 2318)  ... displaying 10 of 13 total bound parameter sets ...  ('["Unreachable_via_tool"]', 2351), ('["shipping_delivery"]', 2353)]
2025-07-29 19:51:51,676 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 19:51:51,676 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-07-29 19:51:51][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Updated 23 database records with hard classification
[2025-07-29 19:51:51][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Hard classification summary: 23 total URLs across 10 categories
[2025-07-29 19:51:51][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Hard classification category breakdown: {'home_page': 1, 'about_us': 1, 'terms_and_condition': 0, 'returns_cancellation_exchange': 5, 'privacy_policy': 0, 'shipping_delivery': 6, 'contact_us': 1, 'catalogue': 6, 'urls_not_reachable': 0, 'Unreachable_via_tool': 3}
[2025-07-29 19:51:51][url_classification_208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Successfully saved hard classification results to database
[2025-07-29 19:51:51][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Hard classification completed
{
  "categories_found": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "catalogue",
    "urls_not_reachable",
    "Unreachable_via_tool"
  ]
}
[2025-07-29 19:51:51][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Hard classification results - detailed breakdown
{
  "category_counts": {
    "home_page": 1,
    "about_us": 1,
    "terms_and_condition": 0,
    "returns_cancellation_exchange": 5,
    "privacy_policy": 0,
    "shipping_delivery": 6,
    "contact_us": 1,
    "catalogue": 6,
    "urls_not_reachable": 0,
    "Unreachable_via_tool": 3
  },
  "total_reachable_urls": 23,
  "priority_reachable_urls": 8,
  "total_unreachable_urls": 0,
  "unreachable_percentage": "0.0%"
}
[2025-07-29 19:51:51][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Some URLs are reachable - continuing with normal flow
{
  "total_reachable_urls": 23,
  "priority_urls_found": 8,
  "decision": "NORMAL_FLOW_CONTINUES",
  "reachable_categories": [
    "home_page",
    "about_us",
    "returns_cancellation_exchange",
    "shipping_delivery",
    "contact_us",
    "catalogue"
  ]
}
[2025-07-29 19:51:51][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Starting priority URL filtering
{
  "input_categories": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "catalogue",
    "urls_not_reachable",
    "Unreachable_via_tool"
  ],
  "priority_categories_defined": [
    "catalogue",
    "about_us",
    "products",
    "home_page"
  ]
}
[2025-07-29 19:51:51][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Filtering priority URLs for MCC analysis - input analysis
{
  "total_categories": 10,
  "input_category_counts": {
    "home_page": 1,
    "about_us": 1,
    "terms_and_condition": 0,
    "returns_cancellation_exchange": 5,
    "privacy_policy": 0,
    "shipping_delivery": 6,
    "contact_us": 1,
    "catalogue": 6,
    "urls_not_reachable": 0,
    "Unreachable_via_tool": 3
  },
  "priority_categories": [
    "catalogue",
    "about_us",
    "products",
    "home_page"
  ],
  "max_urls_for_mcc": 18
}
[2025-07-29 19:51:51][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: URLs per category calculated: 18
[2025-07-29 19:51:51][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] DEBUG: Processing category 'home_page': 1 URLs
{
  "category": "home_page",
  "url_count": 1,
  "is_priority": true,
  "has_urls": true
}
[2025-07-29 19:51:51][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: ✅ Added 1 URLs for priority category 'home_page'
{
  "category": "home_page",
  "added_urls": 1,
  "total_available": 1,
  "urls_per_category_limit": 18
}
[2025-07-29 19:51:51][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] DEBUG: Processing category 'about_us': 1 URLs
{
  "category": "about_us",
  "url_count": 1,
  "is_priority": true,
  "has_urls": true
}
[2025-07-29 19:51:51][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: ✅ Added 1 URLs for priority category 'about_us'
{
  "category": "about_us",
  "added_urls": 1,
  "total_available": 1,
  "urls_per_category_limit": 18
}
[2025-07-29 19:51:51][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] DEBUG: Processing category 'terms_and_condition': 0 URLs
{
  "category": "terms_and_condition",
  "url_count": 0,
  "is_priority": false,
  "has_urls": false
}
[2025-07-29 19:51:51][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] DEBUG: Processing category 'returns_cancellation_exchange': 5 URLs
{
  "category": "returns_cancellation_exchange",
  "url_count": 5,
  "is_priority": false,
  "has_urls": true
}
[2025-07-29 19:51:51][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] DEBUG: Processing category 'privacy_policy': 0 URLs
{
  "category": "privacy_policy",
  "url_count": 0,
  "is_priority": false,
  "has_urls": false
}
[2025-07-29 19:51:51][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] DEBUG: Processing category 'shipping_delivery': 6 URLs
{
  "category": "shipping_delivery",
  "url_count": 6,
  "is_priority": false,
  "has_urls": true
}
[2025-07-29 19:51:51][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] DEBUG: Processing category 'contact_us': 1 URLs
{
  "category": "contact_us",
  "url_count": 1,
  "is_priority": false,
  "has_urls": true
}
[2025-07-29 19:51:51][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] DEBUG: Processing category 'catalogue': 6 URLs
{
  "category": "catalogue",
  "url_count": 6,
  "is_priority": true,
  "has_urls": true
}
[2025-07-29 19:51:51][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: ✅ Added 6 URLs for priority category 'catalogue'
{
  "category": "catalogue",
  "added_urls": 6,
  "total_available": 6,
  "urls_per_category_limit": 18
}
[2025-07-29 19:51:51][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] DEBUG: Processing category 'urls_not_reachable': 0 URLs
{
  "category": "urls_not_reachable",
  "url_count": 0,
  "is_priority": false,
  "has_urls": false
}
[2025-07-29 19:51:51][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] DEBUG: Processing category 'Unreachable_via_tool': 3 URLs
{
  "category": "Unreachable_via_tool",
  "url_count": 3,
  "is_priority": false,
  "has_urls": true
}
[2025-07-29 19:51:51][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Priority URLs filtered
{
  "priority_categories": [
    "home_page",
    "about_us",
    "catalogue"
  ],
  "total_priority_urls": 8,
  "fallback_applied": false
}
[2025-07-29 19:51:51][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Priority URL filtering completed
{
  "priority_url_counts": {
    "home_page": 1,
    "about_us": 1,
    "catalogue": 6
  },
  "total_priority_urls": 8
}
[2025-07-29 19:51:51][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Data preparation completed
{
  "total_classified_urls": 23,
  "priority_urls_count": 8
}
[2025-07-29 19:51:51][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Successfully unpacked data preparation result
{
  "classified_urls_type": "<class 'dict'>",
  "priority_urls_type": "<class 'dict'>",
  "classified_urls_is_none": false,
  "priority_urls_is_none": false
}
[2025-07-29 19:51:51][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Step 2 completed in 26.71 seconds
[2025-07-29 19:51:51][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Step 3: Starting MCC classification
{
  "website": "https://drynotch.com",
  "priority_url_categories": [
    "home_page",
    "about_us",
    "catalogue"
  ],
  "total_priority_urls": 8
}
[2025-07-29 19:51:51][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Starting MCC classification
{
  "website": "https://drynotch.com",
  "priority_categories": [
    "home_page",
    "about_us",
    "catalogue"
  ]
}
[2025-07-29 19:51:51][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: 🔍 Extracting text content from priority URLs for analysis
[2025-07-29 19:51:51][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: 📄 Extracting text from home_page: https://drynotch.com
[2025-07-29 19:51:51][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Extracting text using Playwright for: https://drynotch.com
[2025-07-29 19:51:51][text_extraction][NO_REF] INFO: Starting text extraction
{
  "url": "https://drynotch.com",
  "timeout": 60
}
[2025-07-29 19:51:59][text_extraction][NO_REF] WARNING: JavaScript security challenge detected
{
  "url": "https://drynotch.com"
}
[2025-07-29 19:51:59][text_extraction][NO_REF] WARNING: Page appears to be blocked with direct connection
{
  "url": "https://drynotch.com",
  "retry": 0
}
[2025-07-29 19:51:59][text_extraction][NO_REF] INFO: Retrying direct connection in 1.50 seconds
[2025-07-29 19:52:08][text_extraction][NO_REF] WARNING: JavaScript security challenge detected
{
  "url": "https://drynotch.com"
}
[2025-07-29 19:52:08][text_extraction][NO_REF] WARNING: Page appears to be blocked with direct connection
{
  "url": "https://drynotch.com",
  "retry": 1
}
[2025-07-29 19:52:08][text_extraction][NO_REF] INFO: Attempting proxy connection for text extraction as fallback
[2025-07-29 19:52:08][text_extraction][NO_REF] INFO: Using webshare proxy with valid credentials
[2025-07-29 19:52:44][text_extraction][NO_REF] ERROR: Failed to navigate to URL with proxy for text extraction (timeout)
{
  "error": "{'error': {'error': 'Page.goto: Timeout 36000ms exceeded.\\nCall log:\\n  - navigating to \"https://drynotch.com/\", waiting until \"load\"\\n', 'url': 'https://drynotch.com', 'retry': 0}}",
  "error_type": "dict"
}
Traceback for analysis text_extraction:
Error printing traceback: 'dict' object has no attribute '__traceback__'
[2025-07-29 19:52:44][text_extraction][NO_REF] ERROR: Failed to access https://drynotch.com for text extraction with both direct and proxy connections
[2025-07-29 19:52:44][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] WARNING: ⚠️ Playwright extraction insufficient for home_page, trying requests method
[2025-07-29 19:52:45][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: ✅ Text extracted from home_page: 5000 characters
[2025-07-29 19:52:45][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: 📄 Extracting text from about_us: https://drynotch.com/pages/about-us
[2025-07-29 19:52:45][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Extracting text using Playwright for: https://drynotch.com/pages/about-us
[2025-07-29 19:52:45][text_extraction][NO_REF] INFO: Starting text extraction
{
  "url": "https://drynotch.com/pages/about-us",
  "timeout": 60
}
[2025-07-29 19:52:53][text_extraction][NO_REF] WARNING: JavaScript security challenge detected
{
  "url": "https://drynotch.com/pages/about-us"
}
[2025-07-29 19:52:53][text_extraction][NO_REF] WARNING: Page appears to be blocked with direct connection
{
  "url": "https://drynotch.com/pages/about-us",
  "retry": 0
}
[2025-07-29 19:52:53][text_extraction][NO_REF] INFO: Retrying direct connection in 1.50 seconds
[2025-07-29 19:53:01][text_extraction][NO_REF] WARNING: JavaScript security challenge detected
{
  "url": "https://drynotch.com/pages/about-us"
}
[2025-07-29 19:53:01][text_extraction][NO_REF] WARNING: Page appears to be blocked with direct connection
{
  "url": "https://drynotch.com/pages/about-us",
  "retry": 1
}
[2025-07-29 19:53:01][text_extraction][NO_REF] INFO: Attempting proxy connection for text extraction as fallback
[2025-07-29 19:53:01][text_extraction][NO_REF] INFO: Using webshare proxy with valid credentials
[2025-07-29 19:53:37][text_extraction][NO_REF] ERROR: Failed to navigate to URL with proxy for text extraction (timeout)
{
  "error": "{'error': {'error': 'Page.goto: Timeout 36000ms exceeded.\\nCall log:\\n  - navigating to \"https://drynotch.com/pages/about-us\", waiting until \"load\"\\n', 'url': 'https://drynotch.com/pages/about-us', 'retry': 0}}",
  "error_type": "dict"
}
Traceback for analysis text_extraction:
Error printing traceback: 'dict' object has no attribute '__traceback__'
[2025-07-29 19:53:37][text_extraction][NO_REF] ERROR: Failed to access https://drynotch.com/pages/about-us for text extraction with both direct and proxy connections
[2025-07-29 19:53:37][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] WARNING: ⚠️ Playwright extraction insufficient for about_us, trying requests method
[2025-07-29 19:53:38][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: ✅ Text extracted from about_us: 2172 characters
[2025-07-29 19:53:38][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: 📄 Extracting text from catalogue: https://drynotch.com/products/blue-crush-co-ord-set#judgeme_product_reviews
[2025-07-29 19:53:38][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Extracting text using Playwright for: https://drynotch.com/products/blue-crush-co-ord-set#judgeme_product_reviews
[2025-07-29 19:53:38][text_extraction][NO_REF] INFO: Starting text extraction
{
  "url": "https://drynotch.com/products/blue-crush-co-ord-set#judgeme_product_reviews",
  "timeout": 60
}
[2025-07-29 19:54:04][text_extraction][NO_REF] WARNING: Networkidle timeout, falling back to domcontentloaded
{
  "url": "https://drynotch.com/products/blue-crush-co-ord-set#judgeme_product_reviews",
  "retry": 0,
  "wait_error": "Timeout 20000ms exceeded."
}
[2025-07-29 19:54:04][text_extraction][NO_REF] WARNING: JavaScript security challenge detected
{
  "url": "https://drynotch.com/products/blue-crush-co-ord-set#judgeme_product_reviews"
}
[2025-07-29 19:54:04][text_extraction][NO_REF] WARNING: Page appears to be blocked with direct connection
{
  "url": "https://drynotch.com/products/blue-crush-co-ord-set#judgeme_product_reviews",
  "retry": 0
}
[2025-07-29 19:54:04][text_extraction][NO_REF] INFO: Retrying direct connection in 1.50 seconds
[2025-07-29 19:54:31][text_extraction][NO_REF] WARNING: Networkidle timeout, falling back to domcontentloaded
{
  "url": "https://drynotch.com/products/blue-crush-co-ord-set#judgeme_product_reviews",
  "retry": 1,
  "wait_error": "Timeout 20000ms exceeded."
}
[2025-07-29 19:54:31][text_extraction][NO_REF] WARNING: JavaScript security challenge detected
{
  "url": "https://drynotch.com/products/blue-crush-co-ord-set#judgeme_product_reviews"
}
[2025-07-29 19:54:31][text_extraction][NO_REF] WARNING: Page appears to be blocked with direct connection
{
  "url": "https://drynotch.com/products/blue-crush-co-ord-set#judgeme_product_reviews",
  "retry": 1
}
[2025-07-29 19:54:31][text_extraction][NO_REF] INFO: Attempting proxy connection for text extraction as fallback
[2025-07-29 19:54:31][text_extraction][NO_REF] INFO: Using webshare proxy with valid credentials
[2025-07-29 19:54:38][text_extraction][NO_REF] ERROR: Text extraction timed out after 60 seconds for URL: https://drynotch.com/products/blue-crush-co-ord-set#judgeme_product_reviews
[2025-07-29 19:54:38][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] WARNING: ⚠️ Playwright extraction insufficient for catalogue, trying requests method
[2025-07-29 19:54:39][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: ✅ Text extracted from catalogue: 4799 characters
[2025-07-29 19:54:39][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: 📊 Text extraction completed: 3 categories with content
[2025-07-29 19:54:39][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Website information attempt 1
{
  "website": "https://drynotch.com"
}
[2025-07-29 19:54:39][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: 📊 URLs being passed to Gemini for website summary
{
  "total_urls_count": 8,
  "urls_breakdown_by_category": {
    "home_page": 1,
    "about_us": 1,
    "catalogue": 6
  },
  "priority_urls_structure": {
    "home_page": [
      "https://drynotch.com"
    ],
    "about_us": [
      "https://drynotch.com/pages/about-us"
    ],
    "catalogue": [
      "https://drynotch.com/products/blue-crush-co-ord-set#judgeme_product_reviews",
      "https://drynotch.com/collections"
    ]
  },
  "max_urls_for_mcc_limit": 18,
  "extracted_content_categories": [
    "home_page",
    "about_us",
    "catalogue"
  ],
  "total_extracted_text_length": 11971
}
[2025-07-29 19:54:39][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: ✅ Using extracted text content for website analysis2025-07-29 19:54:41,322 - google_genai.models - INFO - AFC is enabled with max remote calls: 20000.
2025-07-29 19:54:47,436 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 200 OK"
2025-07-29 19:54:47,446 - google_genai.models - INFO - AFC remote call 1 is done.
2025-07-29 19:54:54,491 - google_genai.models - INFO - AFC is enabled with max remote calls: 20000.
2025-07-29 19:55:04,595 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 200 OK"
2025-07-29 19:55:04,602 - google_genai.models - INFO - AFC remote call 1 is done.

[2025-07-29 19:54:39][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Calling Gemini API for website information...
[2025-07-29 19:54:41][mcc_analysis_unknown][NO_REF] INFO: Starting Gemini API call
{
  "model": "gemini-2.5-flash",
  "timeout": 90,
  "max_retries": 3,
  "prompt_length": 9790,
  "context": {
    "task_type": "mcc_analysis"
  }
}
[2025-07-29 19:54:41][mcc_analysis_unknown][NO_REF] INFO: Gemini API attempt 1/3
[2025-07-29 19:54:47][mcc_analysis_unknown][NO_REF] INFO: Gemini API Usage: cache_tokens_details=None cached_content_token_count=None candidates_token_count=335 candidates_tokens_details=None prompt_token_count=2436 prompt_tokens_details=[ModalityTokenCount(
  modality=<MediaModality.TEXT: 'TEXT'>,
  token_count=2436
)] thoughts_token_count=422 tool_use_prompt_token_count=None tool_use_prompt_tokens_details=None total_token_count=3193 traffic_type=None
[2025-07-29 19:54:47][mcc_analysis_unknown][NO_REF] INFO: Gemini API call successful
{
  "attempt": 1,
  "response_length": 1438,
  "finish_reason": "STOP"
}
[2025-07-29 19:54:52][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Website info response length: 1438
[2025-07-29 19:54:52][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Received Gemini API response for website information
[2025-07-29 19:54:52][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Successfully parsed JSON response
{
  "keys": [
    "is_valid_website",
    "website_redirection",
    "product_services",
    "line_of_business",
    "customers",
    "website_description"
  ]
}
[2025-07-29 19:54:52][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: 📋 WEBSITE SUMMARY OUTPUT FROM GEMINI
{
  "website": "https://drynotch.com",
  "total_urls_analyzed": 8,
  "summary_content": {
    "product_services": "DryNotch offers a range of activewear products including Co-ord sets (e.g., Fiery Trio Co-ord Set, Citrus Blaze Co-ord Set, Flex-on Quarter Zip Co-ord Set, Neon Attack Co-ord Set, Sleek Monochrome Set, Blue Crush Co-ord Set), Tops, Sports Bras, Crop tops, and Bottoms. These products are crafted from cutting-edge fabrics with features such as cloudsoft fabric, criss-cross bra designs, mesh detailing on leggings, removable padded cups for sports bras, 4-way stretch, breathability, seamless construction, moisture-wicking properties, sweat-proof, and squat-proof designs. The fabric composition is 85% Polyester and 15% Spandex.",
    "line_of_business": "Apparel and Activewear Retail",
    "customers": "The target customers are B2C individuals, specifically active women who seek high-quality, stylish, comfortable, and affordable activewear for workouts and everyday wear.",
    "website_description": "DryNotch is an online activewear brand that provides meticulously crafted activewear for women, focusing on the fusion of fashion and function. The website showcases a variety of activewear products like co-ord sets, tops, sports bras, crop tops, and bottoms, emphasizing comfort, performance, and durability through advanced fabric technologies. It aims to empower customers with confidence during their active lifestyles."
  },
  "summary_lengths": {
    "product_services_chars": 630,
    "line_of_business_chars": 29,
    "customers_chars": 170,
    "website_description_chars": 423
  }
}
[2025-07-29 19:54:52][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Website information extracted successfully
{
  "info_keys": [
    "product_services",
    "line_of_business",
    "customers",
    "website_description"
  ]
}
[2025-07-29 19:54:52][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: MCC classification attempt 1
{
  "website": "https://drynotch.com"
}
[2025-07-29 19:54:52][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Loading CSV data files...
[2025-07-29 19:54:52][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Loaded special_mccs.csv with 57 rows
[2025-07-29 19:54:52][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Loaded combined_mcc.csv with 303 rows
[2025-07-29 19:54:52][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: CSV data processing completed
[2025-07-29 19:54:52][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Calling Gemini API for MCC classification...
[2025-07-29 19:54:54][mcc_analysis_unknown][NO_REF] INFO: Starting Gemini API call
{
  "model": "gemini-2.5-flash",
  "timeout": 90,
  "max_retries": 3,
  "prompt_length": 223351,
  "context": {
    "task_type": "mcc_analysis"
  }
}
[2025-07-29 19:54:54][mcc_analysis_unknown][NO_REF] INFO: Gemini API attempt 1/3
[2025-07-29 19:55:04][mcc_analysis_unknown][NO_REF] INFO: Gemini API Usage: cache_tokens_details=None cached_content_token_count=None candidates_token_count=238 candidates_tokens_details=None prompt_token_count=46712 prompt_tokens_details=[ModalityTokenCount(
  modality=<MediaModality.TEXT: 'TEXT'>,
  token_count=46712
)] thoughts_token_count=1209 tool_use_prompt_token_count=None tool_use_prompt_tokens_details=None total_token_count=48159 traffic_type=None
[2025-07-29 19:55:04][mcc_analysis_unknown][NO_REF] INFO: Gemini API call successful
{
  "attempt": 1,
  "response_length": 941,
  "finish_reason": "STOP"
}
[2025-07-29 19:55:09][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Received Gemini API response for MCC classification
[2025-07-29 19:55:09][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Successfully parsed JSON response
{
  "keys": [
    "mcc",
    "business_desc",
    "business_category",
    "reason",
    "website"
  ]
}
[2025-07-29 19:55:09][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: MCC classification retrieved successfully
{
  "mcc": "5655",
  "business_desc": "DryNotch is an online activewear brand specializing in high-quality, stylish, and comfortable activewear for women, including co-ord sets, tops, sports bras, crop tops, and bottoms, designed for workouts and everyday wear.",
  "business_category": "Sports apparels",
  "reason": "The business, DryNotch, explicitly states its 'line_of_business' as 'Apparel and Activewear Retail' and its 'product_services' include 'activewear products' for 'active women' for 'workouts and everyday wear'. This directly matches the description of MCC 5655 'Sports apparels', which is defined as 'Business selling sports apparel' for 'Anyone amongst Men, Women, Boys, Girls'. Although the target customer is specifically women, the primary classification is based on the type of apparel, which is sports/activewear, making 5655 the most specific and appropriate MCC from the 'special_mcc_data'."
}
[2025-07-29 19:55:09][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Step 3: MCC classification completed in 197.92 seconds
{
  "website_info_available": true,
  "mcc_info_available": true,
  "processing_time": "197.92s"
}
[2025-07-29 19:55:09][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: ✅ Website content appears valid - continuing with normal flow
{
  "inactive_fields": 0,
  "valid_fields": 4,
  "threshold_check": "passed"
}
[2025-07-29 19:55:09][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Step 4: Saving results to database
[2025-07-29 19:55:09][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Saving MCC analysis results to database
{
  "website": "https://drynotch.com"
}
2025-07-29 19:55:09,604 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = ?
2025-07-29 19:55:09,604 - sqlalchemy.engine.Engine - INFO - SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = ?
2025-07-29 19:55:09,604 INFO sqlalchemy.engine.Engine [cached since 226.3s ago] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd',)
2025-07-29 19:55:09,604 - sqlalchemy.engine.Engine - INFO - [cached since 226.3s ago] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd',)
2025-07-29 19:55:09,605 INFO sqlalchemy.engine.Engine UPDATE mcc_analysis_gemini SET mcc_code=?, business_category=?, business_description=?, reasoning=?, completed_at=?, details=?, processing_status=? WHERE mcc_analysis_gemini.id = ?
2025-07-29 19:55:09,605 - sqlalchemy.engine.Engine - INFO - UPDATE mcc_analysis_gemini SET mcc_code=?, business_category=?, business_description=?, reasoning=?, completed_at=?, details=?, processing_status=? WHERE mcc_analysis_gemini.id = ?
2025-07-29 19:55:09,605 INFO sqlalchemy.engine.Engine [generated in 0.00013s] ('5655', 'Sports apparels', '', "The business, DryNotch, explicitly states its 'line_of_business' as 'Apparel and Activewear Retail' and its 'product_services' include 'activewear pr ... (299 characters truncated) ... ification is based on the type of apparel, which is sports/activewear, making 5655 the most specific and appropriate MCC from the 'special_mcc_data'.", '2025-07-29T19:55:09.605035Z', '{"website": "https://drynotch.com", "scrape_request_ref_id": "208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd", "website_info": {"product_services": ... (3922 characters truncated) ... lysis", "content_availability_status": "content_available", "fallback_method_used": false, "text_extraction_used": false, "insufficient_data": false}', 'COMPLETED', 29)
2025-07-29 19:55:09,605 - sqlalchemy.engine.Engine - INFO - [generated in 0.00013s] ('5655', 'Sports apparels', '', "The business, DryNotch, explicitly states its 'line_of_business' as 'Apparel and Activewear Retail' and its 'product_services' include 'activewear pr ... (299 characters truncated) ... ification is based on the type of apparel, which is sports/activewear, making 5655 the most specific and appropriate MCC from the 'special_mcc_data'.", '2025-07-29T19:55:09.605035Z', '{"website": "https://drynotch.com", "scrape_request_ref_id": "208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd", "website_info": {"product_services": ... (3922 characters truncated) ... lysis", "content_availability_status": "content_available", "fallback_method_used": false, "text_extraction_used": false, "insufficient_data": false}', 'COMPLETED', 29)
2025-07-29 19:55:09,606 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 19:55:09,606 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-29 19:55:09,834 - httpx - INFO - HTTP Request: PATCH https://bffapi.biztel.ai/api/mcc/results "HTTP/1.1 401 "
[2025-07-29 19:55:09][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: MCC analysis results saved successfully
{
  "analysis_id": 29
}
[2025-07-29 19:55:09][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: 🚀 Preparing MCC results webhook with correct format
{
  "scrape_request_ref_id": "208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd",
  "website": "https://drynotch.com",
  "analysis_id": 29
}
[2025-07-29 19:55:09][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: 📦 MCC WEBHOOK PAYLOAD CONSTRUCTION - STEP BY STEP
{
  "construction_step": "1_mcc_payload_created",
  "timestamp": "2025-07-29T19:55:09.618031",
  "website": "https://drynotch.com",
  "scrape_request_ref_id": "208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd",
  "mcc_code": "5655",
  "business_category": "Sports apparels",
  "business_description_length": 0,
  "payload_keys": [
    "website",
    "createdDate",
    "status",
    "scrapeRequestUuid",
    "mcc",
    "manualMcc",
    "businessCategory",
    "businessDescription"
  ],
  "payload_structure": {
    "website": "https://drynotch.com",
    "scrapeRequestUuid": "208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd",
    "status": "COMPLETED",
    "mcc": 5655,
    "manualMcc": -1,
    "businessCategory": "Sports apparels",
    "businessDescription_length": 0,
    "createdDate": "2025-07-29T19:55:09.618023Z"
  },
  "complete_payload_json": {
    "website": "https://drynotch.com",
    "createdDate": "2025-07-29T19:55:09.618023Z",
    "status": "COMPLETED",
    "scrapeRequestUuid": "208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd",
    "mcc": 5655,
    "manualMcc": -1,
    "businessCategory": "Sports apparels",
    "businessDescription": ""
  }
}
[2025-07-29 19:55:09][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: 📤 Sending MCC PATCH request with clean headers - DETAILED DEBUG
{
  "url": "https://bffapi.biztel.ai/api/mcc/results",
  "method": "PATCH",
  "payload": {
    "website": "https://drynotch.com",
    "createdDate": "2025-07-29T19:55:09.618023Z",
    "status": "COMPLETED",
    "scrapeRequestUuid": "208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd",
    "mcc": 5655,
    "manualMcc": -1,
    "businessCategory": "Sports apparels",
    "businessDescription": ""
  },
  "headers_being_sent": {
    "X-API-KEY": "12345678",
    "Content-Type": "application/json"
  },
  "api_key_in_headers": "12345678",
  "content_type_in_headers": "application/json",
  "api_key_length": 8
}
[2025-07-29 19:55:09][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: 🔥 MCC PATCH REQUEST - FINAL TRANSMISSION
{
  "construction_step": "2_sending_mcc_request",
  "timestamp": "2025-07-29T19:55:09.622829",
  "about_to_send": true,
  "final_url": "https://bffapi.biztel.ai/api/mcc/results",
  "final_method": "PATCH",
  "final_headers": {
    "X-API-KEY": "12345678",
    "Content-Type": "application/json"
  },
  "final_payload": {
    "website": "https://drynotch.com",
    "createdDate": "2025-07-29T19:55:09.618023Z",
    "status": "COMPLETED",
    "scrapeRequestUuid": "208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd",
    "mcc": 5655,
    "manualMcc": -1,
    "businessCategory": "Sports apparels",
    "businessDescription": ""
  },
  "final_timeout": 30.0,
  "curl_equivalent": "curl -X PATCH 'https://bffapi.biztel.ai/api/mcc/results' -H 'Content-Type: application/json' -H 'X-API-KEY: 12345678' -d '{\"website\": \"https://drynotch.com\", \"createdDate\": \"2025-07-29T19:55:09.618023Z\", \"status\": \"COMPLETED\", \"scrapeRequestUuid\": \"208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd\", \"mcc\": 5655, \"manualMcc\": -1, \"businessCategory\": \"Sports apparels\", \"businessDescription\": \"\"}'"
}
[2025-07-29 19:55:09][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: ⚡ MCC PATCH RESPONSE - IMMEDIATE RESULT
{
  "construction_step": "3_mcc_response_received",
  "timestamp": "2025-07-29T19:55:09.835151",
  "response_status_code": 401,
  "response_headers": {
    "server": "nginx/1.22.1",
    "date": "Tue, 29 Jul 2025 14:25:09 GMT",
    "content-length": "0",
    "connection": "keep-alive",
    "vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers",
    "x-content-type-options": "nosniff",
    "x-xss-protection": "0",
    "cache-control": "no-cache, no-store, max-age=0, must-revalidate",
    "pragma": "no-cache",
    "expires": "0",
    "x-frame-options": "DENY",
    "www-authenticate": "Basic realm=\"Realm\""
  },
  "response_text": "",
  "response_success": false,
  "response_size": 0,
  "request_url": "https://bffapi.biztel.ai/api/mcc/results"
}
[2025-07-29 19:55:09][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: 📥 MCC PATCH response received
{
  "status_code": 401,
  "response_text": "",
  "response_headers": {
    "server": "nginx/1.22.1",
    "date": "Tue, 29 Jul 2025 14:25:09 GMT",
    "content-length": "0",
    "connection": "keep-alive",
    "vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers",
    "x-content-type-options": "nosniff",
    "x-xss-protection": "0",
    "cache-control": "no-cache, no-store, max-age=0, must-revalidate",
    "pragma": "no-cache",
    "expires": "0",
    "x-frame-options": "DENY",
    "www-authenticate": "Basic realm=\"Realm\""
  },
  "url": "https://bffapi.biztel.ai/api/mcc/results"
}
[2025-07-29 19:55:09][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] ERROR: ❌ MCC results webhook failed - Authentication error
{
  "error": "{'status_code': 401, 'response': '', 'url': 'https://bffapi.biztel.ai/api/mcc/results', 'error': 'Invalid API key - check BIZTEL_API_KEY in .env file', 'api_key_length': 8}",
  "error_type": "dict"
}
Traceback for analysis 208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd:
Error printing traceback: 'dict' object has no attribute '__traceback__'
[2025-07-29 19:55:09][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: 🔄 Attempting to send default values despite authentication error
[2025-07-29 19:55:09][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: 📋 Created default payload for authentication error
{
  "payload": {
    "website": "https://drynotch.com",
    "createdDate": "2025-07-29T19:55:09.835297Z",
    "status": "FAILED",
    "scrapeRequestUuid": "208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd",
    "mcc": -1,
    "manualMcc": -1,
    "businessCategory": "authentication_error",
    "businessDescription": "webhook authentication failed"
  }
}
[2025-07-29 19:55:09][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] WARNING: ⚠️ MCC results webhook failed
{
  "analysis_id": 29
}
[2025-07-29 19:55:09][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Saving detailed MCC URL classification results
[2025-07-29 19:55:09][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Saving MCC URL classification results
{
  "analysis_id": 29,
  "soft_categories": 16,
  "hard_categories": 10
}
2025-07-29 19:55:09,835 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 19:55:09,835 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 19:55:09,835 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 19:55:09,835 - sqlalchemy.engine.Engine - INFO - SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 19:55:09,836 INFO sqlalchemy.engine.Engine [cached since 226.5s ago] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd',)
2025-07-29 19:55:09,836 - sqlalchemy.engine.Engine - INFO - [cached since 226.5s ago] ('208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd',)
2025-07-29 19:55:09,842 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:55:09,842 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:55:09,842 INFO sqlalchemy.engine.Engine [generated in 0.00019s (insertmanyvalues) 1/13 (ordered; batch not supported)] (29, '208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com/products/blue-crush-co-ord-set#judgeme_product_reviews', 1, 'products', 'returns_cancellation_exchange,shipping_delivery,catalogue', 'returns_cancellation_exchange,shipping_delivery,catalogue', 1, 0, '2025-07-29T19:55:09.839636Z', 'default')
2025-07-29 19:55:09,842 - sqlalchemy.engine.Engine - INFO - [generated in 0.00019s (insertmanyvalues) 1/13 (ordered; batch not supported)] (29, '208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com/products/blue-crush-co-ord-set#judgeme_product_reviews', 1, 'products', 'returns_cancellation_exchange,shipping_delivery,catalogue', 'returns_cancellation_exchange,shipping_delivery,catalogue', 1, 0, '2025-07-29T19:55:09.839636Z', 'default')
2025-07-29 19:55:09,842 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:55:09,842 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:55:09,842 INFO sqlalchemy.engine.Engine [insertmanyvalues 2/13 (ordered; batch not supported)] (29, '208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com/collections', 1, 'catalogue', 'catalogue', 'catalogue', 1, 0, '2025-07-29T19:55:09.839821Z', 'default')
2025-07-29 19:55:09,842 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 2/13 (ordered; batch not supported)] (29, '208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com/collections', 1, 'catalogue', 'catalogue', 'catalogue', 1, 0, '2025-07-29T19:55:09.839821Z', 'default')
2025-07-29 19:55:09,842 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:55:09,842 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:55:09,842 INFO sqlalchemy.engine.Engine [insertmanyvalues 3/13 (ordered; batch not supported)] (29, '208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com/products/work-it-out-straight-fit-pants', 1, 'products', 'returns_cancellation_exchange,shipping_delivery,catalogue', 'returns_cancellation_exchange,shipping_delivery,catalogue', 1, 0, '2025-07-29T19:55:09.839931Z', 'default')
2025-07-29 19:55:09,842 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 3/13 (ordered; batch not supported)] (29, '208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com/products/work-it-out-straight-fit-pants', 1, 'products', 'returns_cancellation_exchange,shipping_delivery,catalogue', 'returns_cancellation_exchange,shipping_delivery,catalogue', 1, 0, '2025-07-29T19:55:09.839931Z', 'default')
2025-07-29 19:55:09,842 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:55:09,842 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:55:09,842 INFO sqlalchemy.engine.Engine [insertmanyvalues 4/13 (ordered; batch not supported)] (29, '208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 1, 'home_page', 'home_page', 'home_page', 1, 1, '2025-07-29T19:55:09.840035Z', 'default')
2025-07-29 19:55:09,842 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 4/13 (ordered; batch not supported)] (29, '208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com', 1, 'home_page', 'home_page', 'home_page', 1, 1, '2025-07-29T19:55:09.840035Z', 'default')
2025-07-29 19:55:09,853 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:55:09,853 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:55:09,853 INFO sqlalchemy.engine.Engine [insertmanyvalues 5/13 (ordered; batch not supported)] (29, '208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com/products/fiery-trio-co-ord-set', 1, 'products', 'returns_cancellation_exchange,shipping_delivery,catalogue', 'returns_cancellation_exchange,shipping_delivery,catalogue', 1, 0, '2025-07-29T19:55:09.840133Z', 'default')
2025-07-29 19:55:09,853 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 5/13 (ordered; batch not supported)] (29, '208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com/products/fiery-trio-co-ord-set', 1, 'products', 'returns_cancellation_exchange,shipping_delivery,catalogue', 'returns_cancellation_exchange,shipping_delivery,catalogue', 1, 0, '2025-07-29T19:55:09.840133Z', 'default')
2025-07-29 19:55:09,853 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:55:09,853 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:55:09,853 INFO sqlalchemy.engine.Engine [insertmanyvalues 6/13 (ordered; batch not supported)] (29, '208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com/pages/contact', 1, 'contact_us', 'contact_us', 'contact_us', 0, 0, '2025-07-29T19:55:09.840226Z', 'default')
2025-07-29 19:55:09,853 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 6/13 (ordered; batch not supported)] (29, '208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com/pages/contact', 1, 'contact_us', 'contact_us', 'contact_us', 0, 0, '2025-07-29T19:55:09.840226Z', 'default')
2025-07-29 19:55:09,853 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:55:09,853 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:55:09,853 INFO sqlalchemy.engine.Engine [insertmanyvalues 7/13 (ordered; batch not supported)] (29, '208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com/products/neon-attack-co-ord-set', 1, 'products', 'returns_cancellation_exchange,shipping_delivery,catalogue', 'returns_cancellation_exchange,shipping_delivery,catalogue', 1, 0, '2025-07-29T19:55:09.840316Z', 'default')
2025-07-29 19:55:09,853 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 7/13 (ordered; batch not supported)] (29, '208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com/products/neon-attack-co-ord-set', 1, 'products', 'returns_cancellation_exchange,shipping_delivery,catalogue', 'returns_cancellation_exchange,shipping_delivery,catalogue', 1, 0, '2025-07-29T19:55:09.840316Z', 'default')
2025-07-29 19:55:09,853 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:55:09,853 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:55:09,853 INFO sqlalchemy.engine.Engine [insertmanyvalues 8/13 (ordered; batch not supported)] (29, '208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com/products/notchflex-high-waist-leggings-blue-crush', 1, 'products', 'returns_cancellation_exchange,shipping_delivery,catalogue', 'returns_cancellation_exchange,shipping_delivery,catalogue', 1, 0, '2025-07-29T19:55:09.840417Z', 'default')
2025-07-29 19:55:09,853 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 8/13 (ordered; batch not supported)] (29, '208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com/products/notchflex-high-waist-leggings-blue-crush', 1, 'products', 'returns_cancellation_exchange,shipping_delivery,catalogue', 'returns_cancellation_exchange,shipping_delivery,catalogue', 1, 0, '2025-07-29T19:55:09.840417Z', 'default')
2025-07-29 19:55:09,853 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:55:09,853 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:55:09,854 INFO sqlalchemy.engine.Engine [insertmanyvalues 9/13 (ordered; batch not supported)] (29, '208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com/policies/terms-of-service', 1, 'terms_and_condition', 'Unreachable_via_tool', 'Unreachable_via_tool', 0, 0, '2025-07-29T19:55:09.840508Z', 'default')
2025-07-29 19:55:09,854 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 9/13 (ordered; batch not supported)] (29, '208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com/policies/terms-of-service', 1, 'terms_and_condition', 'Unreachable_via_tool', 'Unreachable_via_tool', 0, 0, '2025-07-29T19:55:09.840508Z', 'default')
2025-07-29 19:55:09,854 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:55:09,854 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:55:09,854 INFO sqlalchemy.engine.Engine [insertmanyvalues 10/13 (ordered; batch not supported)] (29, '208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com/policies/refund-policy', 1, 'returns_cancellation_exchange', 'Unreachable_via_tool', 'Unreachable_via_tool', 0, 0, '2025-07-29T19:55:09.840601Z', 'default')
2025-07-29 19:55:09,854 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 10/13 (ordered; batch not supported)] (29, '208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com/policies/refund-policy', 1, 'returns_cancellation_exchange', 'Unreachable_via_tool', 'Unreachable_via_tool', 0, 0, '2025-07-29T19:55:09.840601Z', 'default')
2025-07-29 19:55:09,854 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:55:09,854 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:55:09,854 INFO sqlalchemy.engine.Engine [insertmanyvalues 11/13 (ordered; batch not supported)] (29, '208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com/pages/about-us', 1, 'about_us', 'about_us', 'about_us', 1, 0, '2025-07-29T19:55:09.840691Z', 'default')
2025-07-29 19:55:09,854 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 11/13 (ordered; batch not supported)] (29, '208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com/pages/about-us', 1, 'about_us', 'about_us', 'about_us', 1, 0, '2025-07-29T19:55:09.840691Z', 'default')
2025-07-29 19:55:09,854 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:55:09,854 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:55:09,854 INFO sqlalchemy.engine.Engine [insertmanyvalues 12/13 (ordered; batch not supported)] (29, '208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com/pages/shipping', 1, 'shipping_delivery', 'shipping_delivery', 'shipping_delivery', 0, 0, '2025-07-29T19:55:09.840780Z', 'default')
2025-07-29 19:55:09,854 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 12/13 (ordered; batch not supported)] (29, '208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com/pages/shipping', 1, 'shipping_delivery', 'shipping_delivery', 'shipping_delivery', 0, 0, '2025-07-29T19:55:09.840780Z', 'default')
2025-07-29 19:55:09,854 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:55:09,854 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 19:55:09,854 INFO sqlalchemy.engine.Engine [insertmanyvalues 13/13 (ordered; batch not supported)] (29, '208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com/policies/privacy-policy', 1, 'privacy_policy', 'Unreachable_via_tool', 'Unreachable_via_tool', 0, 0, '2025-07-29T19:55:09.840870Z', 'default')
2025-07-29 19:55:09,854 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 13/13 (ordered; batch not supported)] (29, '208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd', 'https://drynotch.com/policies/privacy-policy', 1, 'privacy_policy', 'Unreachable_via_tool', 'Unreachable_via_tool', 0, 0, '2025-07-29T19:55:09.840870Z', 'default')
2025-07-29 19:55:09,854 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 19:55:09,854 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-29 19:55:09,857 - app.routers.mcc_analysis - INFO - MCC analysis task 29 completed with status: COMPLETED
[2025-07-29 19:55:09][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: MCC URL classification results saved successfully
{
  "analysis_id": 29,
  "urls_saved": 13
}
[2025-07-29 19:55:09][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: MCC URL classification results saved successfully
[2025-07-29 19:55:09][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: Step 4 completed in 0.25 seconds
[2025-07-29 19:55:09][208378df-cfasdf32-4b91-aaea-f15sdfgasdff9cfeddcd][NO_REF] INFO: MCC analysis completed successfully
{
  "analysis_id": 29,
  "processing_time": "224.89s",
  "mcc": [
    "5655"
  ]
}
2025-07-29 19:55:09,857 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 19:55:09,857 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 19:55:09,857 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id AS mcc_analysis_gemini_id, mcc_analysis_gemini.website AS mcc_analysis_gemini_website, mcc_analysis_gemini.scrape_request_ref_id AS mcc_analysis_gemini_scrape_request_ref_id, mcc_analysis_gemini.result_status AS mcc_analysis_gemini_result_status, mcc_analysis_gemini.mcc_code AS mcc_analysis_gemini_mcc_code, mcc_analysis_gemini.business_category AS mcc_analysis_gemini_business_category, mcc_analysis_gemini.business_description AS mcc_analysis_gemini_business_description, mcc_analysis_gemini.reasoning AS mcc_analysis_gemini_reasoning, mcc_analysis_gemini.created_at AS mcc_analysis_gemini_created_at, mcc_analysis_gemini.started_at AS mcc_analysis_gemini_started_at, mcc_analysis_gemini.completed_at AS mcc_analysis_gemini_completed_at, mcc_analysis_gemini.failed_at AS mcc_analysis_gemini_failed_at, mcc_analysis_gemini.last_updated AS mcc_analysis_gemini_last_updated, mcc_analysis_gemini.error_message AS mcc_analysis_gemini_error_message, mcc_analysis_gemini.details AS mcc_analysis_gemini_details, mcc_analysis_gemini.org_id AS mcc_analysis_gemini_org_id, mcc_analysis_gemini.processing_status AS mcc_analysis_gemini_processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = ?
2025-07-29 19:55:09,857 - sqlalchemy.engine.Engine - INFO - SELECT mcc_analysis_gemini.id AS mcc_analysis_gemini_id, mcc_analysis_gemini.website AS mcc_analysis_gemini_website, mcc_analysis_gemini.scrape_request_ref_id AS mcc_analysis_gemini_scrape_request_ref_id, mcc_analysis_gemini.result_status AS mcc_analysis_gemini_result_status, mcc_analysis_gemini.mcc_code AS mcc_analysis_gemini_mcc_code, mcc_analysis_gemini.business_category AS mcc_analysis_gemini_business_category, mcc_analysis_gemini.business_description AS mcc_analysis_gemini_business_description, mcc_analysis_gemini.reasoning AS mcc_analysis_gemini_reasoning, mcc_analysis_gemini.created_at AS mcc_analysis_gemini_created_at, mcc_analysis_gemini.started_at AS mcc_analysis_gemini_started_at, mcc_analysis_gemini.completed_at AS mcc_analysis_gemini_completed_at, mcc_analysis_gemini.failed_at AS mcc_analysis_gemini_failed_at, mcc_analysis_gemini.last_updated AS mcc_analysis_gemini_last_updated, mcc_analysis_gemini.error_message AS mcc_analysis_gemini_error_message, mcc_analysis_gemini.details AS mcc_analysis_gemini_details, mcc_analysis_gemini.org_id AS mcc_analysis_gemini_org_id, mcc_analysis_gemini.processing_status AS mcc_analysis_gemini_processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = ?
2025-07-29 19:55:09,858 INFO sqlalchemy.engine.Engine [cached since 224.9s ago] (29,)
2025-07-29 19:55:09,858 - sqlalchemy.engine.Engine - INFO - [cached since 224.9s ago] (29,)
2025-07-29 19:55:09,858 INFO sqlalchemy.engine.Engine UPDATE mcc_analysis_gemini SET completed_at=? WHERE mcc_analysis_gemini.id = ?
2025-07-29 19:55:09,858 - sqlalchemy.engine.Engine - INFO - UPDATE mcc_analysis_gemini SET completed_at=? WHERE mcc_analysis_gemini.id = ?
2025-07-29 19:55:09,858 INFO sqlalchemy.engine.Engine [generated in 0.00010s] ('2025-07-29T19:55:09.858218Z', 29)
2025-07-29 19:55:09,858 - sqlalchemy.engine.Engine - INFO - [generated in 0.00010s] ('2025-07-29T19:55:09.858218Z', 29)
2025-07-29 19:55:09,858 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 19:55:09,858 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-29 19:55:09,860 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 19:55:09,860 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 19:55:09,861 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id AS mcc_analysis_gemini_id, mcc_analysis_gemini.website AS mcc_analysis_gemini_website, mcc_analysis_gemini.scrape_request_ref_id AS mcc_analysis_gemini_scrape_request_ref_id, mcc_analysis_gemini.result_status AS mcc_analysis_gemini_result_status, mcc_analysis_gemini.mcc_code AS mcc_analysis_gemini_mcc_code, mcc_analysis_gemini.business_category AS mcc_analysis_gemini_business_category, mcc_analysis_gemini.business_description AS mcc_analysis_gemini_business_description, mcc_analysis_gemini.reasoning AS mcc_analysis_gemini_reasoning, mcc_analysis_gemini.created_at AS mcc_analysis_gemini_created_at, mcc_analysis_gemini.started_at AS mcc_analysis_gemini_started_at, mcc_analysis_gemini.completed_at AS mcc_analysis_gemini_completed_at, mcc_analysis_gemini.failed_at AS mcc_analysis_gemini_failed_at, mcc_analysis_gemini.last_updated AS mcc_analysis_gemini_last_updated, mcc_analysis_gemini.error_message AS mcc_analysis_gemini_error_message, mcc_analysis_gemini.details AS mcc_analysis_gemini_details, mcc_analysis_gemini.org_id AS mcc_analysis_gemini_org_id, mcc_analysis_gemini.processing_status AS mcc_analysis_gemini_processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = ?
2025-07-29 19:55:09,861 - sqlalchemy.engine.Engine - INFO - SELECT mcc_analysis_gemini.id AS mcc_analysis_gemini_id, mcc_analysis_gemini.website AS mcc_analysis_gemini_website, mcc_analysis_gemini.scrape_request_ref_id AS mcc_analysis_gemini_scrape_request_ref_id, mcc_analysis_gemini.result_status AS mcc_analysis_gemini_result_status, mcc_analysis_gemini.mcc_code AS mcc_analysis_gemini_mcc_code, mcc_analysis_gemini.business_category AS mcc_analysis_gemini_business_category, mcc_analysis_gemini.business_description AS mcc_analysis_gemini_business_description, mcc_analysis_gemini.reasoning AS mcc_analysis_gemini_reasoning, mcc_analysis_gemini.created_at AS mcc_analysis_gemini_created_at, mcc_analysis_gemini.started_at AS mcc_analysis_gemini_started_at, mcc_analysis_gemini.completed_at AS mcc_analysis_gemini_completed_at, mcc_analysis_gemini.failed_at AS mcc_analysis_gemini_failed_at, mcc_analysis_gemini.last_updated AS mcc_analysis_gemini_last_updated, mcc_analysis_gemini.error_message AS mcc_analysis_gemini_error_message, mcc_analysis_gemini.details AS mcc_analysis_gemini_details, mcc_analysis_gemini.org_id AS mcc_analysis_gemini_org_id, mcc_analysis_gemini.processing_status AS mcc_analysis_gemini_processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = ?
2025-07-29 19:55:09,861 INFO sqlalchemy.engine.Engine [generated in 0.00011s] (29,)
2025-07-29 19:55:09,861 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] (29,)
2025-07-29 19:55:09,861 - app.routers.mcc_analysis - INFO - Updated MccAnalysis 29 final status to: COMPLETED
2025-07-29 19:55:09,861 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-29 19:55:09,861 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-29 19:55:09,861 - app.routers.mcc_analysis - INFO - === BACKGROUND TASK COMPLETED SUCCESSFULLY for analysis ID: 29 ===
